'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPI<PERSON>ard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { MapPin, Calendar, Users, TrendingUp, Quote, ArrowRight, Filter, Heart } from 'lucide-react'

interface SuccessStory {
  id: string
  title: string
  summary: string
  fullStory: string
  image: string
  location: string
  county: string
  category: string
  date: string
  impact: {
    metric: string
    value: string
  }
  testimonial: {
    quote: string
    author: string
    role: string
    image?: string
  }
  tags: string[]
  featured: boolean
}

interface NPISuccessStoriesGridProps {
  title?: string
  description?: string
  stories?: SuccessStory[]
}

export const NPISuccessStoriesGridBlock: React.FC<NPISuccessStoriesGridProps> = ({
  title = 'Transforming Lives Across Kenya',
  description = 'From rural villages to urban centers, discover how traditional knowledge and community innovation are creating lasting change.',
  stories = [
    {
      id: 'aloe-cooperative-baringo',
      title: 'Aloe Vera Cooperative Transforms Rural Economy in Baringo',
      summary:
        "A women's cooperative in Baringo County successfully commercialized traditional aloe vera products, increasing household incomes by 300% and creating 150 jobs.",
      fullStory:
        "In the arid landscapes of Baringo County, the Chepkemoi Women's Cooperative has transformed traditional aloe vera knowledge into a thriving business...",
      image: '/assets/product 1.jpg',
      location: 'Marigat, Baringo County',
      county: 'Baringo',
      category: 'Community-Led Innovation',
      date: '2023-08-15',
      impact: {
        metric: 'Income Increase',
        value: '300%',
      },
      testimonial: {
        quote:
          'NPI helped us turn our traditional knowledge into a thriving business. Now our children can go to school and our community has hope for the future.',
        author: 'Mary Chepkemoi',
        role: 'Cooperative Chairwoman',
      },
      tags: ['women empowerment', 'traditional medicine', 'cooperative', 'income generation'],
      featured: true,
    },
    {
      id: 'moringa-youth-turkana',
      title: 'Youth-Led Moringa Processing Enterprise in Turkana',
      summary:
        'Young entrepreneurs in Turkana developed a sustainable moringa processing facility, creating nutritious products while preserving traditional knowledge.',
      fullStory:
        'In the challenging environment of Turkana County, a group of young entrepreneurs has created something remarkable...',
      image: '/assets/product 2.jpg',
      location: 'Lodwar, Turkana County',
      county: 'Turkana',
      category: 'Youth Empowerment',
      date: '2023-07-22',
      impact: {
        metric: 'Youth Employed',
        value: '50',
      },
      testimonial: {
        quote:
          "We learned to combine our ancestors' wisdom with modern technology. Now we're exporting moringa products across East Africa.",
        author: 'John Ekale',
        role: 'Youth Group Leader',
      },
      tags: ['youth', 'nutrition', 'export', 'technology'],
      featured: false,
    },
    {
      id: 'maasai-medicine-kajiado',
      title: 'Maasai Traditional Medicine IP Protection Success',
      summary:
        'Maasai community successfully registered traditional medicine formulations, securing intellectual property rights and establishing sustainable revenue streams.',
      fullStory:
        'The Maasai community of Kajiado has achieved a historic milestone in protecting their traditional knowledge...',
      image: '/assets/product 3.jpg',
      location: 'Kajiado County',
      county: 'Kajiado',
      category: 'IP Registration',
      date: '2023-09-10',
      impact: {
        metric: 'Patents Registered',
        value: '5',
      },
      testimonial: {
        quote:
          'Our traditional knowledge is now protected by law. We can share it with the world while ensuring our community benefits.',
        author: 'Elder Joseph Sankale',
        role: 'Traditional Healer',
      },
      tags: ['intellectual property', 'traditional medicine', 'legal protection'],
      featured: false,
    },
    {
      id: 'honey-harvesting-nakuru',
      title: 'Ogiek Community Honey Enterprise in Mau Forest',
      summary:
        'The Ogiek community transformed traditional beekeeping knowledge into a sustainable honey enterprise, generating income while conserving the forest.',
      fullStory:
        'Deep in the Mau Forest, the Ogiek community has turned their ancient beekeeping traditions into a modern success story...',
      image: '/assets/product 4.jpg',
      location: 'Mau Forest, Nakuru County',
      county: 'Nakuru',
      category: 'Environmental Conservation',
      date: '2023-06-18',
      impact: {
        metric: 'Forest Area Protected',
        value: '500 hectares',
      },
      testimonial: {
        quote:
          'Our honey business helps us protect the forest that our ancestors have called home for generations.',
        author: 'Sarah Lekorere',
        role: 'Beekeeping Group Leader',
      },
      tags: ['beekeeping', 'forest conservation', 'indigenous community'],
      featured: false,
    },
    {
      id: 'textile-weaving-machakos',
      title: 'Traditional Textile Weaving Revival in Machakos',
      summary:
        'Women in Machakos County revived traditional textile weaving techniques, creating a fashion brand that celebrates Kamba culture.',
      fullStory:
        'The ancient art of Kamba textile weaving was nearly lost until a group of determined women decided to revive it...',
      image: '/assets/product 5.jpg',
      location: 'Machakos Town, Machakos County',
      county: 'Machakos',
      category: 'Cultural Preservation',
      date: '2023-05-25',
      impact: {
        metric: 'Artisans Trained',
        value: '80',
      },
      testimonial: {
        quote:
          "We're not just making textiles, we're preserving our culture and creating opportunities for the next generation.",
        author: 'Grace Mutindi',
        role: 'Master Weaver',
      },
      tags: ['textiles', 'cultural preservation', 'fashion', 'women'],
      featured: false,
    },
    {
      id: 'medicinal-plants-meru',
      title: 'Medicinal Plants Conservation Project in Meru',
      summary:
        'Traditional healers in Meru established a medicinal plants garden, preserving endangered species while training new healers.',
      fullStory:
        'In the foothills of Mount Kenya, traditional healers are working to preserve both their knowledge and the plants that make it possible...',
      image: '/assets/product 6.jpg',
      location: 'Meru County',
      county: 'Meru',
      category: 'Knowledge Preservation',
      date: '2023-04-12',
      impact: {
        metric: 'Plant Species Conserved',
        value: '120',
      },
      testimonial: {
        quote:
          'By preserving these plants, we preserve our healing traditions for future generations.',
        author: 'Dr. Peter Murithi',
        role: 'Traditional Healer',
      },
      tags: ['medicinal plants', 'conservation', 'traditional healing'],
      featured: false,
    },
  ],
}) => {
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedCounty, setSelectedCounty] = useState('All Counties')

  const categories = ['All Categories', ...Array.from(new Set(stories.map((s) => s.category)))]
  const counties = ['All Counties', ...Array.from(new Set(stories.map((s) => s.county)))]

  const filteredStories = stories.filter((story) => {
    return (
      (selectedCategory === 'All Categories' || story.category === selectedCategory) &&
      (selectedCounty === 'All Counties' || story.county === selectedCounty)
    )
  })

  const featuredStory = stories.find((story) => story.featured)
  const regularStories = filteredStories.filter((story) => !story.featured)

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium font-npi">Filter Stories:</span>
          </div>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">County</label>
              <select
                value={selectedCounty}
                onChange={(e) => setSelectedCounty(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {counties.map((county) => (
                  <option key={county} value={county}>
                    {county}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Featured Story */}
      {featuredStory &&
        selectedCategory === 'All Categories' &&
        selectedCounty === 'All Counties' && (
          <div className="mb-12">
            <h3 className="text-2xl font-bold mb-6 font-npi flex items-center gap-2">
              <Heart className="w-6 h-6 text-primary" />
              Featured Story
            </h3>
            <NPICard className="overflow-hidden hover:shadow-xl transition-all duration-300">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative h-64 lg:h-auto">
                  <Image
                    src={featuredStory.image}
                    alt={featuredStory.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium">
                      Featured
                    </span>
                  </div>
                </div>

                <div className="p-8">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <span className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {featuredStory.location}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(featuredStory.date).toLocaleDateString()}
                    </span>
                  </div>

                  <NPICardTitle className="text-2xl mb-4">{featuredStory.title}</NPICardTitle>

                  <p className="text-muted-foreground mb-6 leading-relaxed font-npi">
                    {featuredStory.summary}
                  </p>

                  <div className="bg-primary/5 p-4 rounded-lg mb-6">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="w-5 h-5 text-primary" />
                      <span className="font-semibold text-primary font-npi">
                        {featuredStory.impact.metric}
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-primary font-npi">
                      {featuredStory.impact.value}
                    </div>
                  </div>

                  <blockquote className="border-l-4 border-primary pl-4 mb-6">
                    <Quote className="w-5 h-5 text-primary mb-2" />
                    <p className="italic text-muted-foreground mb-2 font-npi">
                      &ldquo;{featuredStory.testimonial.quote}&rdquo;
                    </p>
                    <footer className="text-sm">
                      <strong className="font-npi">{featuredStory.testimonial.author}</strong>
                      <span className="text-muted-foreground font-npi">
                        , {featuredStory.testimonial.role}
                      </span>
                    </footer>
                  </blockquote>

                  <NPIButton asChild variant="primary">
                    <Link href={`/success-stories/${featuredStory.id}`}>
                      Read Full Story <ArrowRight className="w-4 h-4 ml-2" />
                    </Link>
                  </NPIButton>
                </div>
              </div>
            </NPICard>
          </div>
        )}

      {/* Stories Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {regularStories.map((story) => (
          <NPICard
            key={story.id}
            className="overflow-hidden hover:shadow-xl transition-all duration-300 h-full flex flex-col"
          >
            <div className="relative h-56 w-full flex-shrink-0">
              <Image src={story.image} alt={story.title} fill className="object-cover" />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-3 left-3 right-3">
                <div className="text-white text-sm font-medium mb-1 font-npi">{story.category}</div>
                <div className="text-white/80 text-sm font-npi">{story.location}</div>
              </div>
            </div>

            <div className="flex flex-col flex-grow">
              <NPICardHeader className="pb-3">
                <NPICardTitle className="text-base leading-tight line-clamp-2 mb-2">
                  {story.title}
                </NPICardTitle>
                <div className="flex flex-col gap-1 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(story.date).toLocaleDateString()}
                  </span>
                  <span className="text-primary font-medium font-npi text-xs">
                    {story.impact.metric}: {story.impact.value}
                  </span>
                </div>
              </NPICardHeader>

              <NPICardContent className="flex-grow pb-3">
                <p className="text-muted-foreground leading-relaxed mb-3 font-npi text-sm line-clamp-3">
                  {story.summary}
                </p>

                <blockquote className="border-l-2 border-primary pl-2 italic text-xs mb-3">
                  <p className="mb-1 font-npi line-clamp-2">
                    &ldquo;{story.testimonial.quote.substring(0, 80)}...&rdquo;
                  </p>
                  <footer className="text-muted-foreground font-npi text-xs">
                    <strong>{story.testimonial.author}</strong>
                  </footer>
                </blockquote>

                <div className="flex flex-wrap gap-1">
                  {story.tags.slice(0, 2).map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-primary/10 text-primary text-xs font-npi"
                      style={{ borderRadius: '0' }}
                    >
                      {tag}
                    </span>
                  ))}
                  {story.tags.length > 2 && (
                    <span className="text-xs text-muted-foreground font-npi">
                      +{story.tags.length - 2}
                    </span>
                  )}
                </div>
              </NPICardContent>

              <NPICardFooter className="mt-auto pt-3">
                <NPIButton
                  asChild
                  variant="outline"
                  className="w-full"
                  style={{ borderRadius: '0' }}
                >
                  <Link href={`/success-stories/${story.id}`}>
                    Read Full Story <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </NPIButton>
              </NPICardFooter>
            </div>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-muted-foreground font-npi">
          Showing {filteredStories.length} of {stories.length} success stories
        </p>
      </div>
    </NPISection>
  )
}
