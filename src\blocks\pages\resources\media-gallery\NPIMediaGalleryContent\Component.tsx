'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { Play, Calendar, Eye, Youtube, ExternalLink } from 'lucide-react'

interface MediaItem {
  id: string
  title: string
  description: string
  type: 'video' | 'photo' | 'document'
  thumbnail: string
  url: string
  date: string
  views?: number
  duration?: string
  category: string
}

interface NPIMediaGalleryContentProps {
  title?: string
  description?: string
  mediaItems?: MediaItem[]
}

export const NPIMediaGalleryContentBlock: React.FC<NPIMediaGalleryContentProps> = ({
  title = 'Media Collection',
  description = 'Explore our comprehensive collection of videos, photos, and multimedia content showcasing NPI initiatives and community impact.',
  mediaItems = [
    {
      id: 'npi-documentary-2024',
      title: 'NPI Documentary: Transforming Communities',
      description:
        'A comprehensive documentary showcasing how NPI initiatives are transforming communities across Kenya.',
      type: 'video',
      thumbnail: '/assets/background.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2024-01-15',
      views: 15420,
      duration: '12:45',
      category: 'Documentary',
    },
    {
      id: 'community-workshop-highlights',
      title: 'Community Workshop Highlights',
      description:
        'Key moments from our community capacity building workshops across different counties.',
      type: 'video',
      thumbnail: '/assets/product 1.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-12-10',
      views: 8750,
      duration: '8:30',
      category: 'Workshops',
    },
    {
      id: 'traditional-knowledge-preservation',
      title: 'Preserving Traditional Knowledge',
      description: 'Documentation of traditional knowledge and practices from various communities.',
      type: 'video',
      thumbnail: '/assets/product 2.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-11-25',
      views: 12100,
      duration: '6:15',
      category: 'Heritage',
    },
    {
      id: 'youth-entrepreneurs-feature',
      title: 'Young Entrepreneurs Leading Change',
      description:
        'Feature story on young entrepreneurs who are innovating in the natural products sector.',
      type: 'video',
      thumbnail: '/assets/product 3.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-09-20',
      views: 6450,
      duration: '10:30',
      category: 'Innovation',
    },
    {
      id: 'research-collaboration',
      title: 'Research Collaboration Success',
      description: 'Highlighting successful research partnerships and their impact on communities.',
      type: 'video',
      thumbnail: '/assets/product 4.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-08-15',
      views: 9200,
      duration: '14:20',
      category: 'Research',
    },
    {
      id: 'sustainable-practices',
      title: 'Sustainable Harvesting Practices',
      description:
        'Educational content on sustainable harvesting and processing of natural products.',
      type: 'video',
      thumbnail: '/assets/product 5.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-07-30',
      views: 11800,
      duration: '9:45',
      category: 'Education',
    },
    {
      id: 'community-impact-stories',
      title: 'Community Impact Stories',
      description: 'Real stories from community members about how NPI has impacted their lives.',
      type: 'video',
      thumbnail: '/assets/product 6.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-06-20',
      views: 7650,
      duration: '11:15',
      category: 'Impact',
    },
    {
      id: 'policy-development',
      title: 'Policy Development Process',
      description:
        'Behind the scenes look at how policies are developed to support natural products industry.',
      type: 'video',
      thumbnail: '/assets/background.jpg',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      date: '2023-05-10',
      views: 5320,
      duration: '16:40',
      category: 'Policy',
    },
  ],
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All')

  const getYouTubeVideoId = (url: string): string | null => {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)
    return match ? match[1] : null
  }

  const getYouTubeEmbedUrl = (url: string): string => {
    const videoId = getYouTubeVideoId(url)
    return videoId ? `https://www.youtube.com/embed/${videoId}` : url
  }

  const formatViews = (views: number): string => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  const categories = ['All', ...Array.from(new Set(mediaItems.map((item) => item.category)))]
  const filteredItems =
    selectedCategory === 'All'
      ? mediaItems
      : mediaItems.filter((item) => item.category === selectedCategory)

  return (
    <NPISection className="bg-[#FFFFFF]">
      <NPISectionHeader>
        <NPISectionTitle className="text-black">{title}</NPISectionTitle>
        <NPISectionDescription className="text-[#725242]">{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-8 justify-center">
        {categories.map((category) => (
          <NPIButton
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className={
              selectedCategory === category
                ? 'bg-[#25718A] text-white hover:bg-[#8A3E25]'
                : 'border-[#725242] text-[#725242] hover:bg-[#725242] hover:text-white'
            }
          >
            {category}
          </NPIButton>
        ))}
      </div>

      {/* Featured Video */}
      {filteredItems.length > 0 && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-6 font-npi text-[#34170D]">Featured Video</h3>
          <NPICard className="overflow-hidden border-l-4 border-[#A7795E]">
            <div className="grid lg:grid-cols-2 gap-0">
              <div className="relative aspect-video">
                <iframe
                  src={getYouTubeEmbedUrl(filteredItems[0].url)}
                  title={filteredItems[0].title}
                  className="w-full h-full"
                  allowFullScreen
                />
              </div>
              <div className="p-6">
                <div className="flex items-center gap-2 mb-3">
                  <Youtube className="w-5 h-5 text-[#6E3C19]" />
                  <span className="text-sm text-[#46372A] font-npi bg-[#CABA9C] px-2 py-1 font-medium">
                    {filteredItems[0].category}
                  </span>
                </div>
                <NPICardTitle className="text-xl mb-3 text-[#34170D]">
                  {filteredItems[0].title}
                </NPICardTitle>
                <p className="text-[#46372A] mb-4 font-npi">{filteredItems[0].description}</p>

                <div className="flex items-center gap-4 text-sm text-[#46372A] mb-4">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(filteredItems[0].date).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {formatViews(filteredItems[0].views || 0)}
                  </span>
                  <span>{filteredItems[0].duration}</span>
                </div>

                <NPIButton asChild className="bg-[#6E3C19] text-[#E5E1DC] hover:bg-[#8A6240]">
                  <Link href={filteredItems[0].url} target="_blank">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Watch on YouTube
                  </Link>
                </NPIButton>
              </div>
            </div>
          </NPICard>
        </div>
      )}

      {/* Video Grid - 4 cards per row */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {filteredItems.slice(1).map((item, index) => {
          // Cycle through 6-color palette
          const colorClasses = [
            'bg-[#8A3E25] text-white', // Reddish brown with white text
            'bg-[#725242] text-white', // Brown with white text
            'bg-[#25718A] text-white', // Blue accent with white text
            'bg-[#EFE3BA] text-black', // Cream with black text
          ]
          const colorClass = colorClasses[index % colorClasses.length]

          return (
            <NPICard
              key={item.id}
              className="overflow-hidden hover:shadow-lg transition-shadow duration-300 border-l-4 border-[#725242]"
            >
              <div className="relative aspect-video group cursor-pointer">
                <Image
                  src={item.thumbnail}
                  alt={item.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors duration-300" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-12 h-12 bg-[#25718A] rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Play className="w-6 h-6 text-white ml-1" />
                  </div>
                </div>
                <div className="absolute bottom-2 right-2 bg-black/80 text-white px-2 py-1 text-xs">
                  {item.duration}
                </div>
                <div className="absolute top-2 left-2">
                  <span className={`${colorClass} px-2 py-1 text-xs font-medium`}>
                    {item.category}
                  </span>
                </div>
              </div>

              <NPICardHeader>
                <NPICardTitle className="text-base leading-tight line-clamp-2 text-black">
                  {item.title}
                </NPICardTitle>
              </NPICardHeader>

              <NPICardContent>
                <p className="text-[#725242] text-sm leading-relaxed mb-4 line-clamp-2 font-npi">
                  {item.description}
                </p>

                <div className="flex items-center justify-between text-xs text-[#725242]">
                  <span className="flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(item.date).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {formatViews(item.views || 0)}
                  </span>
                </div>
              </NPICardContent>
            </NPICard>
          )
        })}
      </div>

      {/* YouTube Channel CTA */}
      <div className="mt-12 text-center">
        <NPICard className="bg-gradient-to-r from-[#EFE3BA] to-[#FFFFFF] border-[#725242]">
          <NPICardContent className="p-8">
            <Youtube className="w-12 h-12 text-[#25718A] mx-auto mb-4" />
            <h3 className="text-xl font-bold mb-2 text-black">Subscribe to Our YouTube Channel</h3>
            <p className="text-[#725242] mb-6 max-w-2xl mx-auto">
              Stay updated with the latest videos, documentaries, and educational content from NPI.
              Subscribe to never miss our newest releases.
            </p>
            <NPIButton asChild size="lg" className="bg-[#25718A] text-white hover:bg-[#8A3E25]">
              <Link href="https://youtube.com/@npi-kenya" target="_blank">
                <Youtube className="w-5 h-5 mr-2" />
                Subscribe Now
              </Link>
            </NPIButton>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
